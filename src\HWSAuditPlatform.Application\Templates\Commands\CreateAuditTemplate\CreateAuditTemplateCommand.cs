using HWSAuditPlatform.Application.Common;

namespace HWSAuditPlatform.Application.Templates.Commands.CreateAuditTemplate;

/// <summary>
/// Command to create a new audit template
/// </summary>
public class CreateAuditTemplateCommand : BaseCommand<int>
{
    /// <summary>
    /// Name of the template
    /// </summary>
    public string TemplateName { get; set; } = string.Empty;

    /// <summary>
    /// Optional description of the template
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Template category for organization
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Instructions for auditors
    /// </summary>
    public string? Instructions { get; set; }

    /// <summary>
    /// Estimated duration in minutes
    /// </summary>
    public int? EstimatedDurationMinutes { get; set; }

    /// <summary>
    /// Whether to enable area-based responsibility assignment
    /// </summary>
    public bool EnableAreaBasedResponsibility { get; set; }

    /// <summary>
    /// Whether to enable finding categorization
    /// </summary>
    public bool EnableFindingCategorization { get; set; }

    /// <summary>
    /// Whether the template should be active upon creation
    /// </summary>
    public bool IsActive { get; set; } = true;
}
