using HWSAuditPlatform.Application.Common;

namespace HWSAuditPlatform.Application.Templates.Commands.UpdateAuditTemplate;

/// <summary>
/// Command to update an existing audit template
/// </summary>
public class UpdateAuditTemplateCommand : BaseCommand
{
    /// <summary>
    /// ID of the template to update
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Name of the template
    /// </summary>
    public string TemplateName { get; set; } = string.Empty;

    /// <summary>
    /// Optional description of the template
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Template category for organization
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Instructions for auditors
    /// </summary>
    public string? Instructions { get; set; }

    /// <summary>
    /// Estimated duration in minutes
    /// </summary>
    public int? EstimatedDurationMinutes { get; set; }

    /// <summary>
    /// Whether to enable area-based responsibility assignment
    /// </summary>
    public bool EnableAreaBasedResponsibility { get; set; }

    /// <summary>
    /// Whether to enable finding categorization
    /// </summary>
    public bool EnableFindingCategorization { get; set; }

    /// <summary>
    /// Whether the template is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Record version for optimistic concurrency control
    /// </summary>
    public byte[] RecordVersion { get; set; } = Array.Empty<byte>();
}
