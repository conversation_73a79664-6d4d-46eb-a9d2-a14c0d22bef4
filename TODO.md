# 📋 HWS Audit Platform - TODO List

> **Last Updated:** 2025-01-08
> **Total Items:** 73
> **Status:** Post MediatR/AutoMapper Migration

---

## 📊 **Overview**

This document contains all TODO items found throughout the codebase that need to be implemented or completed following the recent MediatR and AutoMapper removal.

| Priority | Category | Count | Status |
|----------|----------|-------|--------|
| 🚨 **Critical** | API Services | 13 | ✅ Complete |
| 🔧 **High** | Infrastructure | 8 | ✅ Complete |
| 🎨 **Medium** | Web App | 35 | Pending |
| 📱 **Medium** | PWA | 25 | Pending |

---

## 🚨 **CRITICAL PRIORITY**
*These items block core functionality and should be implemented immediately*

<details>
<summary><strong>🔐 Authentication & User Management</strong> <code>2 items</code></summary>

| File | Line | Description | Effort |
|------|------|-------------|---------|
| `AuthController.cs` | 141 | ✅ Implement user creation service method | 🔴 High |
| `AuthController.cs` | 321 | ✅ Implement user service method for getting user by ID | 🟡 Medium |

</details>

<details>
<summary><strong>📝 Audit Management API</strong> <code>6 items</code></summary>

| File | Line | Description | Effort |
|------|------|-------------|---------|
| `AuditsController.cs` | 355 | ✅ Implement service method for getting audit answers | 🔴 High |
| `AuditsController.cs` | 379 | ✅ Implement service method for getting specific audit answer | 🟡 Medium |
| `AuditsController.cs` | 402 | ✅ Implement service method for deleting audit answer | 🟡 Medium |
| `AuditsController.cs` | 461 | ✅ Implement service method for getting completed audits for review | 🔴 High |
| `AuditsController.cs` | 482 | ✅ Implement service method for getting audit for review | 🔴 High |
| `AuditsController.cs` | 527 | ✅ Implement service method for getting audit history | 🟡 Medium |

</details>

<details>
<summary><strong>⚙️ Application Services</strong> <code>1 item</code></summary>

| File | Line | Description | Effort |
|------|------|-------------|---------|
| `AuditService.cs` | 892 | ✅ Create finding if needed (requires IFindingService implementation) | 🔴 High |

</details>

---

## 🔧 **HIGH PRIORITY**
*Infrastructure and background services that support core functionality*

<details>
<summary><strong>🎯 Domain Event System</strong> <code>2 items</code></summary>

| File | Line | Description | Effort |
|------|------|-------------|---------|
| `DomainEventService.cs` | 9 | ✅ Implement proper domain event handling mechanism to replace MediatR | 🔴 High |
| `DomainEventService.cs` | 26 | ✅ Implement domain event handling logic | 🔴 High |

</details>

<details>
<summary><strong>⏰ Background Services</strong> <code>2 items</code></summary>

| File | Line | Description | Effort |
|------|------|-------------|---------|
| `Worker.cs` | 134 | ✅ Implement AD sync functionality | 🔴 High |
| `Worker.cs` | 161 | ✅ Implement recurring audit generation functionality | 🔴 High |

</details>

<details>
<summary><strong>🏗️ Data Model</strong> <code>1 item</code></summary>

| File | Line | Description | Effort |
|------|------|-------------|---------|
| `Factory.cs` | 23 | Normalize to Processes and FactoryProcesses tables for querying and integrity | 🟡 Medium |

</details>

---

## 🎨 **MEDIUM PRIORITY - WEB APPLICATION**
*User interface features and functionality enhancements*

<details>
<summary><strong>🔍 Finding Management</strong> <code>5 items</code></summary>

| File | Line | Description | Effort |
|------|------|-------------|---------|
| `FindingCard.razor` | 369 | Implement view corrective actions functionality | 🟡 Medium |
| `FindingDetail.razor` | 282 | Implement status update modal | 🟡 Medium |
| `FindingDetail.razor` | 288 | Implement category assignment modal | 🟡 Medium |
| `FindingDetail.razor` | 294 | Implement responsibility assignment modal | 🔴 High |
| `FindingDetail.razor` | 308 | Implement create corrective action functionality | 🔴 High |

</details>

<details>
<summary><strong>⚡ Corrective Actions</strong> <code>9 items</code></summary>

| File | Line | Description | Effort |
|------|------|-------------|---------|
| `CorrectiveActions.razor` | 170 | Load users dynamically | 🟡 Medium |
| `CorrectiveActions.razor` | 417 | Implement "my-actions" filter when user context is available | 🟡 Medium |
| `CorrectiveActions.razor` | 471 | Implement create action modal | 🔴 High |
| `CorrectiveActions.razor` | 477 | Implement status update modal | 🟡 Medium |
| `CorrectiveActions.razor` | 483 | Implement complete action modal | 🟡 Medium |
| `CorrectiveActions.razor` | 489 | Implement verify action modal | 🟡 Medium |
| `CorrectiveActionDetail.razor` | 314 | Implement status update modal | 🟡 Medium |
| `CorrectiveActionDetail.razor` | 320 | Implement complete action modal | 🟡 Medium |
| `CorrectiveActionDetail.razor` | 326 | Implement verify action modal | 🟡 Medium |

</details>

<details>
<summary><strong>🏢 Organization Management</strong> <code>4 items</code></summary>

| File | Line | Description | Effort |
|------|------|-------------|---------|
| `AreaResponsibilityList.razor` | 168 | Implement edit functionality | 🟡 Medium |
| `AreaResponsibilityList.razor` | 174 | Implement delete functionality with confirmation | 🟡 Medium |
| `FindingCategoryList.razor` | 200 | Implement edit functionality | 🟡 Medium |
| `FindingCategoryList.razor` | 206 | Implement delete functionality with confirmation | 🟡 Medium |

</details>

<details>
<summary><strong>👥 User Management</strong> <code>7 items</code></summary>

| File | Line | Description | Effort |
|------|------|-------------|---------|
| `ProcessOwnerAssignmentsComponent.razor` | 185 | Navigate to assignment management page | 🟢 Low |
| `UserListComponent.razor` | 245 | Navigate to user details page | 🟢 Low |
| `UserListComponent.razor` | 251 | Navigate to user edit page | 🟢 Low |
| `Users.razor` | 283 | Implement activate user API call | 🟡 Medium |
| `Users.razor` | 289 | Implement deactivate user API call | 🟡 Medium |

</details>

<details>
<summary><strong>📋 Template Management</strong> <code>9 items</code></summary>

| File | Line | Description | Effort |
|------|------|-------------|---------|
| `RecurringAudits.razor` | 87 | Load templates from API | 🟡 Medium |
| `TemplateList.razor` | 303 | Implement activate template API call | 🟡 Medium |
| `TemplateList.razor` | 309 | Implement deactivate template API call | 🟡 Medium |
| `TemplateQuestions.razor` | 640 | Implement question reordering | 🔴 High |
| `TemplateQuestions.razor` | 646 | Implement question reordering | 🔴 High |
| `TemplateQuestions.razor` | 652 | Implement group editing | 🟡 Medium |
| `TemplateQuestions.razor` | 658 | Implement group deletion | 🟡 Medium |
| `TemplateApiService.cs` | 69 | Update API to include EnableAreaBasedResponsibility in summary DTO | 🟢 Low |

</details>

<details>
<summary><strong>📊 Audit Management</strong> <code>1 item</code></summary>

| File | Line | Description | Effort |
|------|------|-------------|---------|
| `AuditDetail.razor` | 396 | Implement start audit functionality when comprehensive API is available | 🔴 High |

</details>

---

## 📱 **MEDIUM PRIORITY - PWA APPLICATION**
*Mobile audit execution and offline functionality*

<details>
<summary><strong>🎯 Audit Execution</strong> <code>4 items</code></summary>

| File | Line | Description | Effort |
|------|------|-------------|---------|
| `AttachmentDisplay.razor` | 410 | If not found offline, try to get from API (for uploaded attachments) | 🟡 Medium |
| `QuestionAnswerInput.razor` | 406 | Store category assignment for the finding | 🟡 Medium |
| `ResponsibilityAssignmentModal.razor` | 274 | Load available users from API | 🟡 Medium |
| `ResponsibilityAssignmentModal.razor` | 310 | Save assignments via API | 🟡 Medium |

</details>

<details>
<summary><strong>📋 Audit Management</strong> <code>8 items</code></summary>

| File | Line | Description | Effort |
|------|------|-------------|---------|
| `AuditDetails.razor` | 399 | Get actual option text instead of "Option {id}" | 🟢 Low |
| `AuditDetails.razor` | 402 | Get actual option texts for multi-select answers | 🟢 Low |
| `AuditHistory.razor` | 461 | Implement share functionality | 🟡 Medium |
| `AuditList.razor` | 298 | Implement sync functionality | 🔴 High |
| `AuditList.razor` | 463 | Show error message to user | 🟢 Low |
| `AuditList.razor` | 469 | Show error message to user | 🟢 Low |
| `AuditResults.razor` | 535 | Implement export functionality | 🟡 Medium |
| `AuditResults.razor` | 543 | Implement share functionality | 🟡 Medium |

</details>

<details>
<summary><strong>🔄 Navigation & Sync</strong> <code>4 items</code></summary>

| File | Line | Description | Effort |
|------|------|-------------|---------|
| `NavMenu.razor` | 315 | Implement actual sync functionality | 🔴 High |
| `Home.razor` | 234 | Implement comprehensive sync functionality | 🔴 High |
| `Home.razor` | 307 | Show error message to user | 🟢 Low |
| `Home.razor` | 313 | Show error message to user | 🟢 Low |

</details>

<details>
<summary><strong>⚙️ Service Layer</strong> <code>15 items</code></summary>

| File | Line | Description | Effort |
|------|------|-------------|---------|
| `ServiceImplementations.cs` | 604 | Implement actual API call for GetUsersByRole | 🟡 Medium |
| `ServiceImplementations.cs` | 610 | Implement actual API call for GetUsersByArea | 🟡 Medium |
| `ServiceImplementations.cs` | 631 | Implement actual API call for GetFindingCategories | 🟡 Medium |
| `ServiceImplementations.cs` | 637 | Implement actual API call for CreateFindingCategory | 🟡 Medium |
| `ServiceImplementations.cs` | 860 | Implement sync logic | 🔴 High |
| `ServiceImplementations.cs` | 866 | Implement sync status | 🟡 Medium |
| `ServiceImplementations.cs` | 872 | Implement pending items count | 🟡 Medium |
| `ServiceImplementations.cs` | 878 | Implement sync status | 🟡 Medium |
| `ServiceImplementations.cs` | 893 | Implement background sync queueing | 🔴 High |
| `ServiceImplementations.cs` | 900 | Implement manual sync request | 🔴 High |
| `ServiceImplementations.cs` | 907 | Implement getting pending sync items | 🟡 Medium |
| `ServiceImplementations.cs` | 1040 | Implement camera availability check | 🟡 Medium |
| `ServiceImplementations.cs` | 1046 | Implement photo capture | 🔴 High |
| `ServiceImplementations.cs` | 1052 | Implement photo selection | 🟡 Medium |
| `ServiceImplementations.cs` | 1058 | Implement image resizing | 🟡 Medium |

</details>

<details>
<summary><strong>🔄 Sync Service</strong> <code>2 items</code></summary>

| File | Line | Description | Effort |
|------|------|-------------|---------|
| `SyncServiceImplementation.cs` | 267 | Implement pending audits count | 🟡 Medium |
| `SyncServiceImplementation.cs` | 271 | Store and retrieve last sync error | 🟡 Medium |

</details>

---

## � **IMPLEMENTATION ROADMAP**

### **🎯 Phase 1: Core API Services** *(Immediate - Week 1)*
**Blocking Issues - Must Complete First**

- [ ] **AuthController** - User creation and retrieval methods
- [ ] **AuditsController** - Complete audit management API endpoints
- [ ] **AuditService** - Finding creation integration
- [ ] **DomainEventService** - Replace MediatR event handling

**Estimated Effort:** 3-4 days
**Dependencies:** None
**Impact:** Unblocks all other development

### **⚡ Phase 2: Background Services** *(High Priority - Week 2)*
**Infrastructure Foundation**

- [ ] **SchedulerWorker** - AD synchronization
- [ ] **SchedulerWorker** - Recurring audit generation
- [ ] **Domain Events** - Complete event publishing system
- [ ] **Data Model** - Factory process normalization

**Estimated Effort:** 2-3 days
**Dependencies:** Phase 1 completion
**Impact:** Enables automated workflows

### **🎨 Phase 3: Web Application** *(Medium Priority - Weeks 3-4)*
**User Interface & Experience**

- [ ] **Finding Management** - Status updates, categorization, responsibility assignment
- [ ] **Corrective Actions** - Full CRUD operations and workflow
- [ ] **User Management** - Navigation, activation/deactivation
- [ ] **Template Management** - Question reordering, group management
- [ ] **Organization** - Edit/delete functionality with confirmations

**Estimated Effort:** 5-6 days
**Dependencies:** Phase 1-2 completion
**Impact:** Complete management interface

### **📱 Phase 4: PWA Enhancements** *(Medium Priority - Week 5)*
**Mobile & Offline Capabilities**

- [ ] **Sync System** - Comprehensive online/offline synchronization
- [ ] **Camera Integration** - Photo capture and processing
- [ ] **Export/Share** - Data export and sharing capabilities
- [ ] **Error Handling** - User feedback and offline error management

**Estimated Effort:** 4-5 days
**Dependencies:** Phase 1 completion
**Impact:** Complete mobile audit experience

### **✨ Phase 5: Polish & UX** *(Low Priority - Week 6)*
**User Experience Refinements**

- [ ] **Error Messages** - Comprehensive user feedback system
- [ ] **Dynamic Loading** - API-driven dropdowns and lists
- [ ] **Navigation** - Complete page-to-page navigation flows
- [ ] **Confirmations** - User action confirmation dialogs

**Estimated Effort:** 2-3 days
**Dependencies:** Phase 3-4 completion
**Impact:** Professional user experience

---

## � **PROGRESS TRACKING**

| Phase | Items | Completed | Progress | ETA |
|-------|-------|-----------|----------|-----|
| **Phase 1** | 9 | 0 | ![0%](https://progress-bar.dev/0) | Week 1 |
| **Phase 2** | 5 | 0 | ![0%](https://progress-bar.dev/0) | Week 2 |
| **Phase 3** | 35 | 0 | ![0%](https://progress-bar.dev/0) | Week 3-4 |
| **Phase 4** | 33 | 0 | ![0%](https://progress-bar.dev/0) | Week 5 |
| **Phase 5** | 12 | 0 | ![0%](https://progress-bar.dev/0) | Week 6 |
| **TOTAL** | **94** | **0** | ![0%](https://progress-bar.dev/0) | **6 Weeks** |

---

## 📝 **NOTES & CONSIDERATIONS**

### **🔧 Technical Debt**
- Most TODO items stem from the recent MediatR/AutoMapper removal
- Service pattern implementation is the primary architectural change needed
- Domain event handling requires careful design to maintain clean architecture

### **🎯 Critical Dependencies**
- **Phase 1** blocks all other development - prioritize completion
- **API Services** must be implemented before UI features can function
- **Authentication** services are foundational to user management features

### **⚠️ Risk Factors**
- **PWA Sync** complexity may require additional time
- **Camera Integration** depends on browser API compatibility
- **Domain Events** design decisions impact future extensibility

### **🚀 Success Metrics**
- **Phase 1:** All API endpoints return valid responses
- **Phase 2:** Background services run without errors
- **Phase 3:** Complete user workflows function end-to-end
- **Phase 4:** PWA works offline with sync capabilities
- **Phase 5:** Professional UX with proper error handling

---

*Last Updated: 2025-01-08 | Total Estimated Effort: 16-21 days*
