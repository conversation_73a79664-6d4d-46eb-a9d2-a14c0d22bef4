using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Common;
using System.Reflection;

namespace HWSAuditPlatform.Infrastructure.Services;

/// <summary>
/// Service for publishing domain events
/// Replaces MediatR with a simple service locator pattern for domain event handling
/// </summary>
public class DomainEventService : IDomainEventService
{
    private readonly ILogger<DomainEventService> _logger;
    private readonly IServiceProvider _serviceProvider;

    public DomainEventService(ILogger<DomainEventService> logger, IServiceProvider serviceProvider)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
    }

    public async Task PublishAsync(IDomainEvent domainEvent, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Domain event triggered: {EventType}", domainEvent.GetType().Name);

            // Find all handlers for this event type
            var eventType = domainEvent.GetType();
            var handlerType = typeof(IDomainEventHandler<>).MakeGenericType(eventType);
            var handlers = _serviceProvider.GetServices(handlerType);

            var handlerCount = 0;
            foreach (var handler in handlers)
            {
                handlerCount++;
                _logger.LogDebug("Executing handler {HandlerType} for event {EventType}",
                    handler.GetType().Name, eventType.Name);

                // Get the Handle method and invoke it
                var handleMethod = handlerType.GetMethod("Handle");
                if (handleMethod != null)
                {
                    var task = (Task)handleMethod.Invoke(handler, new object[] { domainEvent, cancellationToken })!;
                    await task;
                }
            }

            _logger.LogInformation("Domain event processed: {EventType} with {HandlerCount} handlers",
                eventType.Name, handlerCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing domain event: {EventType}", domainEvent.GetType().Name);
            throw;
        }
    }

    public async Task PublishAsync(IEnumerable<IDomainEvent> domainEvents, CancellationToken cancellationToken = default)
    {
        var events = domainEvents.ToList();

        if (!events.Any())
            return;

        _logger.LogInformation("Processing {EventCount} domain events", events.Count);

        foreach (var domainEvent in events)
        {
            await PublishAsync(domainEvent, cancellationToken);
        }

        _logger.LogInformation("Successfully processed all {EventCount} domain events", events.Count);
    }
}
