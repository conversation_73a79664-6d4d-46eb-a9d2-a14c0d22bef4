using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Templates.DTOs;
using HWSAuditPlatform.Application.Organization.DTOs;
using HWSAuditPlatform.Application.Findings.DTOs;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Domain.Enums;
using Microsoft.Extensions.Logging;

namespace HWSAuditPlatform.Application.Templates.Queries.GetAuditTemplate;

/// <summary>
/// Handler for GetAuditTemplateQuery
/// </summary>
public class GetAuditTemplateQueryHandler : BaseQueryHandler<GetAuditTemplateQuery, AuditTemplateDto>
{
    private readonly IAuditTemplateRepository _templateRepository;

    public GetAuditTemplateQueryHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        ILogger<GetAuditTemplateQueryHandler> logger,
        IAuditTemplateRepository templateRepository)
        : base(context, currentUserService, logger)
    {
        _templateRepository = templateRepository;
    }

    public override async Task<AuditTemplateDto> Handle(
        GetAuditTemplateQuery request,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Getting template with ID: {Id}", request.Id);

        var template = await _templateRepository.GetTemplateWithFullDetailsAsync(request.Id, cancellationToken);
        
        if (template == null)
        {
            Logger.LogWarning("Template with ID {Id} not found", request.Id);
            return null;
        }

        Logger.LogInformation("Successfully retrieved template: {TemplateName} (Version {Version})", 
            template.TemplateName, template.Version);

        // Map to DTO
        var dto = new AuditTemplateDto
        {
            Id = template.Id,
            TemplateName = template.TemplateName,
            Description = template.Description,
            Version = template.Version,
            IsPublished = template.IsPublished,
            IsActive = template.IsActive,
            FullName = $"{template.TemplateName} v{template.Version}",
            CanBeUsed = template.IsPublished && template.IsActive,
            EnableAreaBasedResponsibility = template.EnableAreaBasedResponsibility,
            EnableFindingCategorization = template.EnableFindingCategorization,
            CreatedAt = template.CreatedAt,
            UpdatedAt = template.UpdatedAt,
            CreatedByUserId = template.CreatedByUserId,
            UpdatedByUserId = template.UpdatedByUserId,
            RecordVersion = template.RecordVersion
        };

        // Map question groups
        if (template.QuestionGroups?.Any() == true)
        {
            dto.QuestionGroups = template.QuestionGroups
                .Where(g => g.IsActive)
                .OrderBy(g => g.DisplayOrder)
                .Select(g => new QuestionGroupDto
                {
                    Id = g.Id,
                    AuditTemplateId = g.AuditTemplateId,
                    GroupName = g.GroupName,
                    Description = g.Description,
                    DisplayOrder = g.DisplayOrder,
                    IsActive = g.IsActive,
                    Questions = g.Questions?
                        .Where(q => q.IsActive)
                        .OrderBy(q => q.DisplayOrder)
                        .Select(MapQuestionToDto)
                        .ToList() ?? new List<QuestionDto>()
                })
                .ToList();
        }

        // Map standalone questions (not in groups)
        if (template.Questions?.Any() == true)
        {
            dto.Questions = template.Questions
                .Where(q => q.IsActive && q.QuestionGroupId == null)
                .OrderBy(q => q.DisplayOrder)
                .Select(MapQuestionToDto)
                .ToList();
        }

        // Map finding categories if enabled
        if (template.EnableFindingCategorization && template.FindingCategories?.Any() == true)
        {
            dto.FindingCategories = template.FindingCategories
                .Where(fc => fc.IsActive)
                .OrderBy(fc => fc.DisplayOrder)
                .Select(fc => new FindingCategoryDto
                {
                    Id = fc.Id,
                    CategoryName = fc.CategoryName,
                    Description = fc.Description,
                    DisplayOrder = fc.DisplayOrder,
                    IsActive = fc.IsActive
                })
                .ToList();
        }

        // Map area responsibilities if enabled
        if (template.EnableAreaBasedResponsibility && template.AreaResponsibilities?.Any() == true)
        {
            dto.AreaResponsibilities = template.AreaResponsibilities
                .Where(ar => ar.IsActive)
                .Select(ar => new AreaResponsibilityDto
                {
                    Id = ar.Id,
                    AreaId = ar.AreaId,
                    AreaName = ar.Area?.AreaName ?? "",
                    ResponsibleUserId = ar.ResponsibleUserId,
                    ResponsibleUserName = ar.ResponsibleUser?.Username ?? "",
                    IsActive = ar.IsActive
                })
                .ToList();
        }

        return dto;
    }

    private QuestionDto MapQuestionToDto(Domain.Entities.Templates.Question question)
    {
        var dto = new QuestionDto
        {
            Id = question.Id,
            AuditTemplateId = question.AuditTemplateId,
            QuestionGroupId = question.QuestionGroupId,
            QuestionGroupName = question.QuestionGroup?.GroupName,
            QuestionText = question.QuestionText,
            QuestionType = question.QuestionType,
            DisplayOrder = question.DisplayOrder,
            IsRequired = question.IsRequired,
            Weight = question.Weight,
            HelpText = question.HelpText,
            ParentQuestionId = question.ParentQuestionId,
            TriggerAnswerValue = question.TriggerAnswerValue,
            SeverityLevel = question.SeverityLevel,
            EvidenceRequired = question.EvidenceRequired,
            IsActive = question.IsActive,
            IsConditional = question.ParentQuestionId.HasValue,
            CreatedAt = question.CreatedAt,
            CreatedByUserId = question.CreatedByUserId,
            UpdatedAt = question.UpdatedAt,
            UpdatedByUserId = question.UpdatedByUserId,
            RecordVersion = BitConverter.GetBytes(question.RecordVersion)
        };

        // Map options
        if (question.Options?.Any() == true)
        {
            dto.Options = question.Options
                .Where(o => o.IsActive)
                .OrderBy(o => o.DisplayOrder)
                .Select(o => new QuestionOptionDto
                {
                    Id = o.Id,
                    QuestionId = o.QuestionId,
                    OptionText = o.OptionText,
                    OptionValue = o.OptionValue,
                    DisplayOrder = o.DisplayOrder,
                    IsActive = o.IsActive
                })
                .ToList();
        }

        // Map child questions (conditional questions)
        if (question.ChildQuestions?.Any() == true)
        {
            dto.ChildQuestions = question.ChildQuestions
                .Where(cq => cq.IsActive)
                .OrderBy(cq => cq.DisplayOrder)
                .Select(MapQuestionToDto)
                .ToList();
        }

        // Map allowed evidence types
        if (question.AllowedEvidenceTypes?.Any() == true)
        {
            dto.AllowedEvidenceTypes = question.AllowedEvidenceTypes
                .Where(aet => aet.IsActive)
                .OrderBy(aet => aet.DisplayOrder)
                .Select(aet => new EvidenceTypeDto
                {
                    EvidenceType = aet.EvidenceType,
                    DisplayOrder = aet.DisplayOrder,
                    IsActive = aet.IsActive
                })
                .ToList();
        }

        return dto;
    }
}

/// <summary>
/// Handler for GetAuditActiveTemplateQuery
/// </summary>
public class GetAuditActiveTemplateQueryHandler : BaseQueryHandler<GetAuditActiveTemplateQuery, AuditTemplateDto>
{
    private readonly GetAuditTemplateQueryHandler _baseHandler;

    public GetAuditActiveTemplateQueryHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        ILogger<GetAuditActiveTemplateQueryHandler> logger,
        IAuditTemplateRepository templateRepository)
        : base(context, currentUserService, logger)
    {
        _baseHandler = new GetAuditTemplateQueryHandler(context, currentUserService, 
            logger as ILogger<GetAuditTemplateQueryHandler>, templateRepository);
    }

    public override async Task<AuditTemplateDto> Handle(
        GetAuditActiveTemplateQuery request,
        CancellationToken cancellationToken)
    {
        var baseQuery = new GetAuditTemplateQuery(request.Id);
        var result = await _baseHandler.Handle(baseQuery, cancellationToken);
        
        // Return null if template is not active
        if (result != null && !result.IsActive)
        {
            Logger.LogWarning("Template with ID {Id} exists but is not active", request.Id);
            return null;
        }

        return result;
    }
}
