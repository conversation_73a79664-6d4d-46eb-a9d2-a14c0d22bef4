using Microsoft.Extensions.Logging;
using HWSAuditPlatform.Application.Interfaces;

namespace HWSAuditPlatform.Application.Common;

/// <summary>
/// Base class for service queries that return a value
/// </summary>
/// <typeparam name="TResponse">The type of response returned by the query</typeparam>
public abstract class BaseQuery<TResponse>
{
}

/// <summary>
/// Base class for query handlers
/// </summary>
/// <typeparam name="TQuery">The query type</typeparam>
/// <typeparam name="TResponse">The response type</typeparam>
public abstract class BaseQueryHandler<TQuery, TResponse> where TQuery : BaseQuery<TResponse>
{
    protected readonly IApplicationDbContext Context;
    protected readonly ICurrentUserService CurrentUserService;
    protected readonly ILogger Logger;

    protected BaseQueryHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        ILogger logger)
    {
        Context = context;
        CurrentUserService = currentUserService;
        Logger = logger;
    }

    public abstract Task<TResponse> Handle(TQuery request, CancellationToken cancellationToken);
}
