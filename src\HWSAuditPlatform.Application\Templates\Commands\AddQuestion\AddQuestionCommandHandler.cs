using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Domain.Entities.Templates;
using Microsoft.Extensions.Logging;

namespace HWSAuditPlatform.Application.Templates.Commands.AddQuestion;

/// <summary>
/// Handler for AddQuestionCommand
/// </summary>
public class AddQuestionCommandHandler : BaseCommandHandler<AddQuestionCommand, int>
{
    private readonly IAuditTemplateRepository _templateRepository;

    public AddQuestionCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        ILogger<AddQuestionCommandHandler> logger,
        IAuditTemplateRepository templateRepository)
        : base(context, currentUserService, logger)
    {
        _templateRepository = templateRepository;
    }

    public override async Task<int> Handle(AddQuestionCommand request, CancellationToken cancellationToken)
    {
        Logger.LogInformation("Adding question to template {TemplateId}: {QuestionText}", 
            request.TemplateId, request.QuestionText);

        // Verify template exists and is not published
        var template = await _templateRepository.GetByIdAsync(request.TemplateId, cancellationToken);
        if (template == null)
        {
            throw new NotFoundException($"Template with ID {request.TemplateId} not found");
        }

        if (template.IsPublished)
        {
            throw new InvalidOperationException("Cannot add questions to a published template. Create a new version instead.");
        }

        var currentUserId = CurrentUserService.UserId;
        if (string.IsNullOrEmpty(currentUserId))
        {
            throw new UnauthorizedAccessException("User not authenticated");
        }

        // Create question entity
        var question = new Question
        {
            AuditTemplateId = request.TemplateId,
            QuestionGroupId = request.QuestionGroupId,
            QuestionText = request.QuestionText,
            QuestionType = request.QuestionType,
            DisplayOrder = request.DisplayOrder,
            IsRequired = request.IsRequired,
            Weight = request.Weight,
            HelpText = request.HelpText,
            ParentQuestionId = request.ParentQuestionId,
            TriggerAnswerValue = request.TriggerAnswerValue,
            SeverityLevel = request.SeverityLevel,
            EvidenceRequired = request.EvidenceRequired,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            CreatedByUserId = currentUserId,
            UpdatedAt = DateTime.UtcNow,
            UpdatedByUserId = currentUserId
        };

        // Add question to context
        Context.Questions.Add(question);
        await Context.SaveChangesAsync(cancellationToken);

        // Add question options if provided
        if (request.Options?.Any() == true)
        {
            var options = request.Options.Select(opt => new QuestionOption
            {
                QuestionId = question.Id,
                OptionText = opt.OptionText,
                OptionValue = opt.OptionValue ?? opt.OptionText,
                DisplayOrder = opt.DisplayOrder,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedByUserId = currentUserId,
                UpdatedAt = DateTime.UtcNow,
                UpdatedByUserId = currentUserId
            }).ToList();

            Context.QuestionOptions.AddRange(options);
        }

        // Add evidence type associations if provided
        if (request.AllowedEvidenceTypeIds?.Any() == true)
        {
            var evidenceTypeAssociations = request.AllowedEvidenceTypeIds.Select((etId, index) => new QuestionAllowedEvidenceType
            {
                QuestionId = question.Id,
                EvidenceType = (Domain.Enums.EvidenceType)etId,
                DisplayOrder = index,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }).ToList();

            Context.QuestionAllowedEvidenceTypes.AddRange(evidenceTypeAssociations);
        }

        await Context.SaveChangesAsync(cancellationToken);

        Logger.LogInformation("Successfully added question with ID: {QuestionId} to template {TemplateId}", 
            question.Id, request.TemplateId);

        return question.Id;
    }
}
