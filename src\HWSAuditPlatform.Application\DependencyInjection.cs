using FluentValidation;

using Microsoft.Extensions.DependencyInjection;
using System.Reflection;

using HWSAuditPlatform.Application.Users.Services;
using HWSAuditPlatform.Application.Scheduling.Services;
using HWSAuditPlatform.Application.Templates.Queries.GetAuditTemplates;
using HWSAuditPlatform.Application.Templates.Queries.GetAuditTemplate;
using HWSAuditPlatform.Application.Templates.Commands.CreateAuditTemplate;
using HWSAuditPlatform.Application.Templates.Commands.UpdateAuditTemplate;
using HWSAuditPlatform.Application.Templates.Commands.PublishTemplate;
using HWSAuditPlatform.Application.Templates.Commands.AddQuestion;

namespace HWSAuditPlatform.Application;

/// <summary>
/// Dependency injection configuration for the Application layer
/// </summary>
public static class DependencyInjection
{
    /// <summary>
    /// Adds Application layer services to the dependency injection container
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        var assembly = Assembly.GetExecutingAssembly();

        // Add Application Services
        services.AddScoped<Users.Services.IUserService, Users.Services.UserService>();
        services.AddScoped<Audits.Services.IAuditService, Audits.Services.AuditService>();
        services.AddScoped<HWSAuditPlatform.Domain.Services.ITemplateAccessService, Users.Services.TemplateAccessService>();

        // Add FluentValidation
        services.AddValidatorsFromAssembly(assembly);

        // Cross-cutting concerns will be handled within services or through decorators

        // Add application services
        services.AddScoped<IAdRoleMappingService, AdRoleMappingService>();
        services.AddScoped<IRecurrenceCalculationService, RecurrenceCalculationService>();

        // Add template handlers
        services.AddScoped<GetAuditTemplatesQueryHandler>();
        services.AddScoped<GetAuditTemplateQueryHandler>();
        services.AddScoped<CreateAuditTemplateCommandHandler>();
        services.AddScoped<UpdateAuditTemplateCommandHandler>();
        services.AddScoped<PublishTemplateCommandHandler>();
        services.AddScoped<AddQuestionCommandHandler>();

        return services;
    }
}
