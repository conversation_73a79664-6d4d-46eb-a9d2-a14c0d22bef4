using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Exceptions;
using Microsoft.Extensions.Logging;

namespace HWSAuditPlatform.Application.Templates.Commands.PublishTemplate;

/// <summary>
/// Handler for PublishTemplateCommand
/// </summary>
public class PublishTemplateCommandHandler : BaseCommandHandler<PublishTemplateCommand>
{
    private readonly IAuditTemplateRepository _templateRepository;

    public PublishTemplateCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        ILogger<PublishTemplateCommandHandler> logger,
        IAuditTemplateRepository templateRepository)
        : base(context, currentUserService, logger)
    {
        _templateRepository = templateRepository;
    }

    public override async Task Handle(PublishTemplateCommand request, CancellationToken cancellationToken)
    {
        Logger.LogInformation("Publishing audit template with ID: {Id}", request.Id);

        var template = await _templateRepository.GetTemplateWithFullDetailsAsync(request.Id, cancellationToken);
        if (template == null)
        {
            throw new NotFoundException($"Template with ID {request.Id} not found");
        }

        // Check if template is already published
        if (template.IsPublished)
        {
            Logger.LogWarning("Template with ID {Id} is already published", request.Id);
            return;
        }

        // Validate template has at least one question before publishing
        var hasQuestions = template.Questions?.Any(q => q.IsActive) == true ||
                          template.QuestionGroups?.Any(g => g.IsActive && g.Questions?.Any(q => q.IsActive) == true) == true;

        if (!hasQuestions)
        {
            throw new InvalidOperationException("Cannot publish template without at least one active question");
        }

        var currentUserId = CurrentUserService.UserId;
        if (string.IsNullOrEmpty(currentUserId))
        {
            throw new UnauthorizedAccessException("User not authenticated");
        }

        // Mark template as published
        template.IsPublished = true;
        template.UpdatedAt = DateTime.UtcNow;
        template.UpdatedByUserId = currentUserId;

        await Context.SaveChangesAsync(cancellationToken);
        await Context.SaveChangesAsync(cancellationToken);

        Logger.LogInformation("Successfully published audit template with ID: {Id}, Name: {TemplateName}, Version: {Version}", 
            template.Id, template.TemplateName, template.Version);
    }
}
