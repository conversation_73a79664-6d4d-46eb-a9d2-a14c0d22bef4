using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Templates.DTOs;
using Microsoft.Extensions.Logging;

namespace HWSAuditPlatform.Application.Templates.Queries.GetAuditTemplates;

/// <summary>
/// Handler for GetAuditTemplatesQuery
/// </summary>
public class GetAuditTemplatesQueryHandler : BaseQueryHandler<GetAuditTemplatesQuery, PaginatedResult<AuditTemplateSummaryDto>>
{
    private readonly IAuditTemplateRepository _templateRepository;

    public GetAuditTemplatesQueryHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        ILogger<GetAuditTemplatesQueryHandler> logger,
        IAuditTemplateRepository templateRepository)
        : base(context, currentUserService, logger)
    {
        _templateRepository = templateRepository;
    }

    public override async Task<PaginatedResult<AuditTemplateSummaryDto>> Handle(
        GetAuditTemplatesQuery request,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Getting templates with filters: SearchTerm={SearchTerm}, IsPublished={IsPublished}, IsActive={IsActive}, PageNumber={PageNumber}, PageSize={PageSize}",
            request.SearchTerm, request.IsPublished, request.IsActive, request.PageNumber, request.PageSize);

        // Determine sort direction
        var ascending = request.SortDirection?.ToLower() != "desc";

        // Get paginated templates from repository
        var (templates, totalCount) = await _templateRepository.GetPagedTemplatesWithFiltersAsync(
            request.PageNumber,
            request.PageSize,
            request.SearchTerm,
            request.IsPublished,
            request.IsActive,
            request.CanBeUsed,
            request.SortBy,
            ascending,
            cancellationToken);

        // Map to DTOs
        var templateDtos = templates.Select(template => new AuditTemplateSummaryDto
        {
            Id = template.Id,
            TemplateName = template.TemplateName,
            Description = template.Description,
            Version = template.Version,
            IsPublished = template.IsPublished,
            IsActive = template.IsActive,
            CreatedAt = template.CreatedAt,
            UpdatedAt = template.UpdatedAt,
            CreatedByUserId = template.CreatedByUserId,
            UpdatedByUserId = template.UpdatedByUserId,
            // Calculate question count from loaded questions
            QuestionCount = template.Questions?.Count(q => q.IsActive) ?? 0,
            EnableAreaBasedResponsibility = template.EnableAreaBasedResponsibility,
            EnableFindingCategorization = template.EnableFindingCategorization
        }).ToList();

        Logger.LogInformation("Successfully retrieved {Count} templates out of {TotalCount} total", 
            templateDtos.Count, totalCount);

        return PaginatedResult<AuditTemplateSummaryDto>.Create(
            templateDtos,
            totalCount,
            request.PageNumber,
            request.PageSize);
    }
}
