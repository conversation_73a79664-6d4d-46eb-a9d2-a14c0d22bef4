using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HWSAuditPlatform.Application.Templates.Queries.GetAuditTemplates;
using HWSAuditPlatform.Application.Templates.Queries.GetAuditTemplate;
using HWSAuditPlatform.Application.Templates.Commands.CreateAuditTemplate;
using HWSAuditPlatform.Application.Templates.Commands.UpdateAuditTemplate;
using HWSAuditPlatform.Application.Templates.Commands.PublishTemplate;
using HWSAuditPlatform.Application.Templates.Commands.AddQuestion;
using HWSAuditPlatform.Application.Templates.DTOs;
using HWSAuditPlatform.Application.Common;

namespace HWSAuditPlatform.ApiService.Controllers;

/// <summary>
/// Controller for managing audit templates
/// </summary>
[ApiController]
[Route("api/v1/templates")]
[Authorize]
public class TemplatesController : BaseController
{
    private readonly GetAuditTemplatesQueryHandler _getTemplatesHandler;
    private readonly GetAuditTemplateQueryHandler _getTemplateHandler;
    private readonly CreateAuditTemplateCommandHandler _createTemplateHandler;
    private readonly UpdateAuditTemplateCommandHandler _updateTemplateHandler;
    private readonly PublishTemplateCommandHandler _publishTemplateHandler;
    private readonly AddQuestionCommandHandler _addQuestionHandler;

    public TemplatesController(
        GetAuditTemplatesQueryHandler getTemplatesHandler,
        GetAuditTemplateQueryHandler getTemplateHandler,
        CreateAuditTemplateCommandHandler createTemplateHandler,
        UpdateAuditTemplateCommandHandler updateTemplateHandler,
        PublishTemplateCommandHandler publishTemplateHandler,
        AddQuestionCommandHandler addQuestionHandler,
        ILogger<TemplatesController> logger) : base(logger)
    {
        _getTemplatesHandler = getTemplatesHandler;
        _getTemplateHandler = getTemplateHandler;
        _createTemplateHandler = createTemplateHandler;
        _updateTemplateHandler = updateTemplateHandler;
        _publishTemplateHandler = publishTemplateHandler;
        _addQuestionHandler = addQuestionHandler;
    }
    /// <summary>
    /// Get a paginated list of audit templates with filtering options
    /// </summary>
    /// <param name="request">Query parameters for filtering and pagination</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of audit templates</returns>
    [HttpGet]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(PaginatedResult<AuditTemplateSummaryDto>), 200)]
    public async Task<ActionResult<PaginatedResult<AuditTemplateSummaryDto>>> GetTemplates(
        [FromQuery] GetAuditTemplatesQuery request,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Getting templates with request: {@Request}", request);
        var result = await _getTemplatesHandler.Handle(request, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Get detailed information about a specific audit template
    /// </summary>
    /// <param name="id">Template ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Detailed template information</returns>
    [HttpGet("{id:int}")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(AuditTemplateDto), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<AuditTemplateDto>> GetTemplate(
        int id,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Getting template with ID: {Id}", id);
        var query = new GetAuditTemplateQuery(id);
        var result = await _getTemplateHandler.Handle(query, cancellationToken);
        
        if (result == null)
        {
            return NotFound($"Template with ID {id} not found");
        }
        
        return Success(result);
    }

    /// <summary>
    /// Create a new audit template
    /// </summary>
    /// <param name="command">Template creation data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created template ID</returns>
    [HttpPost]
    [Authorize(Policy = "SystemManagerOrAbove")]
    [ProducesResponseType(typeof(int), 201)]
    [ProducesResponseType(400)]
    [ProducesResponseType(409)]
    public async Task<ActionResult<int>> CreateTemplate(
        CreateAuditTemplateCommand command,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Creating template: {@Command}", command);
        var templateId = await _createTemplateHandler.Handle(command, cancellationToken);
        return CreatedAtAction(nameof(GetTemplate), new { id = templateId }, Success(templateId));
    }

    /// <summary>
    /// Update an existing audit template
    /// </summary>
    /// <param name="id">Template ID</param>
    /// <param name="command">Template update data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success response</returns>
    [HttpPut("{id:int}")]
    [Authorize(Policy = "SystemManagerOrAbove")]
    [ProducesResponseType(204)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    public async Task<IActionResult> UpdateTemplate(
        int id,
        UpdateAuditTemplateCommand command,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Updating template {Id}: {@Command}", id, command);
        command.Id = id;
        await _updateTemplateHandler.Handle(command, cancellationToken);
        return NoContent();
    }

    /// <summary>
    /// Publish a template to make it available for creating audits
    /// </summary>
    /// <param name="id">Template ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success response</returns>
    [HttpPost("{id:int}/publish")]
    [Authorize(Policy = "SystemManagerOrAbove")]
    [ProducesResponseType(204)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    public async Task<IActionResult> PublishTemplate(
        int id,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Publishing template with ID: {Id}", id);
        var command = new PublishTemplateCommand(id);
        await _publishTemplateHandler.Handle(command, cancellationToken);
        return NoContent();
    }

    /// <summary>
    /// Add a question to an audit template
    /// </summary>
    /// <param name="id">Template ID</param>
    /// <param name="command">Question creation data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created question ID</returns>
    [HttpPost("{id:int}/questions")]
    [Authorize(Policy = "SystemManagerOrAbove")]
    [ProducesResponseType(typeof(int), 201)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<int>> AddQuestion(
        int id,
        AddQuestionCommand command,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Adding question to template {TemplateId}: {@Command}", id, command);
        command.TemplateId = id;
        var questionId = await _addQuestionHandler.Handle(command, cancellationToken);
        return Success(questionId);
    }
}
