using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Entities.Templates;
using Microsoft.Extensions.Logging;

namespace HWSAuditPlatform.Application.Templates.Commands.CreateAuditTemplate;

/// <summary>
/// Handler for CreateAuditTemplateCommand
/// </summary>
public class CreateAuditTemplateCommandHandler : BaseCommandHandler<CreateAuditTemplateCommand, int>
{
    private readonly IAuditTemplateRepository _templateRepository;

    public CreateAuditTemplateCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        ILogger<CreateAuditTemplateCommandHandler> logger,
        IAuditTemplateRepository templateRepository)
        : base(context, currentUserService, logger)
    {
        _templateRepository = templateRepository;
    }

    public override async Task<int> Handle(CreateAuditTemplateCommand request, CancellationToken cancellationToken)
    {
        Logger.LogInformation("Creating new audit template: {TemplateName}", request.TemplateName);

        // Check if template with same name already exists
        var existingTemplate = await _templateRepository.GetLatestVersionAsync(
            request.TemplateName, 
            publishedOnly: false, 
            cancellationToken);

        int version = 1;
        if (existingTemplate != null)
        {
            version = existingTemplate.Version + 1;
            Logger.LogInformation("Template {TemplateName} already exists, creating version {Version}", 
                request.TemplateName, version);
        }

        var currentUserId = CurrentUserService.UserId;
        if (string.IsNullOrEmpty(currentUserId))
        {
            throw new UnauthorizedAccessException("User not authenticated");
        }

        // Create new template entity
        var template = new AuditTemplate
        {
            TemplateName = request.TemplateName,
            Description = request.Description,
            Version = version,
            IsPublished = false, // New templates start as unpublished
            IsActive = request.IsActive,
            EnableAreaBasedResponsibility = request.EnableAreaBasedResponsibility,
            EnableFindingCategorization = request.EnableFindingCategorization,
            CreatedAt = DateTime.UtcNow,
            CreatedByUserId = currentUserId,
            UpdatedAt = DateTime.UtcNow,
            UpdatedByUserId = currentUserId
        };

        // Add to repository
        await _templateRepository.AddAsync(template, cancellationToken);
        await Context.SaveChangesAsync(cancellationToken);

        Logger.LogInformation("Successfully created audit template with ID: {Id}, Name: {TemplateName}, Version: {Version}", 
            template.Id, template.TemplateName, template.Version);

        return template.Id;
    }
}
