using HWSAuditPlatform.Application.Users.Services;
using HWSAuditPlatform.Application.Audits.Services;
using HWSAuditPlatform.Application.Interfaces;
using Microsoft.Extensions.Options;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.SchedulerWorker;

public class Worker : BackgroundService
{
    private readonly ILogger<Worker> _logger;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly AdSyncOptions _adSyncOptions;
    private readonly RecurringAuditOptions _recurringAuditOptions;

    public Worker(ILogger<Worker> logger, IServiceScopeFactory serviceScopeFactory,
        IOptions<AdSyncOptions> adSyncOptions, IOptions<RecurringAuditOptions> recurringAuditOptions)
    {
        _logger = logger;
        _serviceScopeFactory = serviceScopeFactory;
        _adSyncOptions = adSyncOptions.Value;
        _recurringAuditOptions = recurringAuditOptions.Value;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Scheduler Worker started. AD Sync - Interval: {AdInterval} minutes, Enabled: {AdEnabled}. Recurring Audits - Interval: {AuditInterval} minutes, Enabled: {AuditEnabled}",
            _adSyncOptions.SyncIntervalMinutes, _adSyncOptions.Enabled, _recurringAuditOptions.GenerationIntervalMinutes, _recurringAuditOptions.Enabled);

        // Validate configuration
        if (!ValidateConfiguration())
        {
            _logger.LogError("Invalid configuration detected. Worker will not start.");
            return;
        }

        // Calculate the intervals
        var adSyncInterval = TimeSpan.FromMinutes(_adSyncOptions.SyncIntervalMinutes);
        var auditGenerationInterval = TimeSpan.FromMinutes(_recurringAuditOptions.GenerationIntervalMinutes);

        // Use a reasonable base interval (minimum 1 minute, maximum of the smaller configured interval)
        var baseInterval = TimeSpan.FromMinutes(Math.Max(1, Math.Min(_adSyncOptions.SyncIntervalMinutes, _recurringAuditOptions.GenerationIntervalMinutes)));

        var lastAdSync = DateTime.MinValue;
        var lastAuditGeneration = DateTime.MinValue;

        _logger.LogInformation("Worker loop starting with base interval: {BaseInterval} minutes", baseInterval.TotalMinutes);

        try
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                var now = DateTime.UtcNow;

                // Check if AD sync is due
                if (_adSyncOptions.Enabled && (now - lastAdSync) >= adSyncInterval)
                {
                    _logger.LogDebug("AD sync is due. Last sync: {LastSync}, Interval: {Interval} minutes",
                        lastAdSync == DateTime.MinValue ? "Never" : lastAdSync.ToString("yyyy-MM-dd HH:mm:ss"),
                        adSyncInterval.TotalMinutes);

                    await PerformAdSyncAsync(stoppingToken);
                    lastAdSync = now;
                }

                // Check if recurring audit generation is due
                if (_recurringAuditOptions.Enabled && (now - lastAuditGeneration) >= auditGenerationInterval)
                {
                    _logger.LogDebug("Recurring audit generation is due. Last generation: {LastGeneration}, Interval: {Interval} minutes",
                        lastAuditGeneration == DateTime.MinValue ? "Never" : lastAuditGeneration.ToString("yyyy-MM-dd HH:mm:ss"),
                        auditGenerationInterval.TotalMinutes);

                    await PerformRecurringAuditGenerationAsync(stoppingToken);
                    lastAuditGeneration = now;
                }

                // Wait for the base interval before checking again
                try
                {
                    await Task.Delay(baseInterval, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation("Worker cancellation requested");
                    break;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogCritical(ex, "Critical error in worker main loop");
            throw;
        }
        finally
        {
            _logger.LogInformation("Scheduler Worker stopped");
        }
    }

    private bool ValidateConfiguration()
    {
        var isValid = true;

        if (_adSyncOptions.SyncIntervalMinutes <= 0)
        {
            _logger.LogError("Invalid AD sync interval: {Interval} minutes. Must be greater than 0.", _adSyncOptions.SyncIntervalMinutes);
            isValid = false;
        }

        if (_recurringAuditOptions.GenerationIntervalMinutes <= 0)
        {
            _logger.LogError("Invalid recurring audit generation interval: {Interval} minutes. Must be greater than 0.", _recurringAuditOptions.GenerationIntervalMinutes);
            isValid = false;
        }

        if (_recurringAuditOptions.MaxAuditsPerBatch <= 0)
        {
            _logger.LogError("Invalid max audits per batch: {MaxAudits}. Must be greater than 0.", _recurringAuditOptions.MaxAuditsPerBatch);
            isValid = false;
        }

        if (_recurringAuditOptions.MaxAuditsPerBatch > 1000)
        {
            _logger.LogWarning("Max audits per batch is very high: {MaxAudits}. Consider reducing to avoid performance issues.", _recurringAuditOptions.MaxAuditsPerBatch);
        }

        return isValid;
    }

    private async Task PerformAdSyncAsync(CancellationToken stoppingToken)
    {
        var startTime = DateTime.UtcNow;
        try
        {
            _logger.LogInformation("Starting scheduled AD user synchronization");

            using var scope = _serviceScopeFactory.CreateScope();
            var userService = scope.ServiceProvider.GetRequiredService<IUserService>();

            // Perform AD sync using the UserService
            var syncResult = await userService.SyncUsersFromActiveDirectoryAsync(
                dryRun: false,
                cancellationToken: stoppingToken);

            var duration = DateTime.UtcNow - startTime;
            _logger.LogInformation("AD sync completed successfully. " +
                "Created: {Created}, Updated: {Updated}, Errors: {Errors}, Duration: {Duration}ms",
                syncResult.UsersCreated, syncResult.UsersUpdated, syncResult.Errors.Count, duration.TotalMilliseconds);

            // Log any errors that occurred during sync
            foreach (var error in syncResult.Errors)
            {
                _logger.LogWarning("AD sync error: {Error}", error);
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("AD sync was cancelled");
        }
        catch (Exception ex)
        {
            var duration = DateTime.UtcNow - startTime;
            _logger.LogError(ex, "Error during scheduled AD user synchronization after {Duration}ms", duration.TotalMilliseconds);
        }
    }

    private async Task PerformRecurringAuditGenerationAsync(CancellationToken stoppingToken)
    {
        var startTime = DateTime.UtcNow;
        try
        {
            _logger.LogInformation("Starting scheduled recurring audit generation (max batch size: {MaxBatch})",
                _recurringAuditOptions.MaxAuditsPerBatch);

            using var scope = _serviceScopeFactory.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<IApplicationDbContext>();
            var auditService = scope.ServiceProvider.GetRequiredService<IAuditService>();

            // Find recurring audit settings that are due for generation
            var today = DateOnly.FromDateTime(DateTime.UtcNow);
            var dueSettings = await context.RecurringAuditSettings
                .Include(r => r.RecurrenceRule)
                .Where(r => r.IsEnabled &&
                           r.NextGenerationDate.HasValue &&
                           r.NextGenerationDate.Value <= today)
                .Take(_recurringAuditOptions.MaxAuditsPerBatch)
                .ToListAsync(stoppingToken);

            var auditsGenerated = 0;
            var settingsProcessed = 0;
            var errors = new List<string>();

            foreach (var setting in dueSettings)
            {
                try
                {
                    settingsProcessed++;

                    // Create audit request based on the recurring setting
                    var createRequest = new CreateAuditRequest
                    {
                        AuditTemplateId = setting.AuditTemplateId,
                        AssignmentType = setting.AssignmentType,
                        AssignedToUserId = setting.AssignToUserId,
                        AssignedToUserGroupId = setting.AssignToUserGroupId,
                        ScheduledDate = DateTime.UtcNow.AddDays(1), // Schedule for tomorrow
                        DueDate = DateTime.UtcNow.AddDays(setting.DeadlineDays),
                        FactoryId = setting.FactoryId,
                        AreaId = setting.AreaId ?? 0,
                        SubAreaId = setting.SubAreaId
                    };

                    var auditId = await auditService.CreateAuditAsync(createRequest, stoppingToken);
                    auditsGenerated++;

                    _logger.LogDebug("Generated audit {AuditId} from recurring setting {SettingId}",
                        auditId, setting.Id);

                    // Update the setting's last generated date and calculate next generation date
                    setting.LastGeneratedAt = DateTime.UtcNow;
                    // TODO: Calculate next generation date based on recurrence rule
                    // For now, just add 7 days as a placeholder
                    setting.NextGenerationDate = today.AddDays(7);

                    await context.SaveChangesAsync(stoppingToken);
                }
                catch (Exception ex)
                {
                    var error = $"Failed to generate audit from setting {setting.Id}: {ex.Message}";
                    errors.Add(error);
                    _logger.LogError(ex, "Error generating audit from recurring setting {SettingId}", setting.Id);
                }
            }

            var duration = DateTime.UtcNow - startTime;
            _logger.LogInformation("Recurring audit generation completed. " +
                "Settings processed: {SettingsProcessed}, Audits generated: {AuditsGenerated}, " +
                "Errors: {ErrorCount}, Duration: {Duration}ms",
                settingsProcessed, auditsGenerated, errors.Count, duration.TotalMilliseconds);

            // Log any errors that occurred during generation
            foreach (var error in errors)
            {
                _logger.LogWarning("Recurring audit generation error: {Error}", error);
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Recurring audit generation was cancelled");
        }
        catch (Exception ex)
        {
            var duration = DateTime.UtcNow - startTime;
            _logger.LogError(ex, "Error during scheduled recurring audit generation after {Duration}ms", duration.TotalMilliseconds);
        }
    }
}

/// <summary>
/// Configuration options for AD synchronization
/// </summary>
public class AdSyncOptions
{
    public const string SectionName = "AdSync";

    /// <summary>
    /// Interval between AD synchronizations in minutes
    /// </summary>
    public int SyncIntervalMinutes { get; set; } = 30;

    /// <summary>
    /// Whether to enable AD synchronization
    /// </summary>
    public bool Enabled { get; set; } = true;
}

/// <summary>
/// Configuration options for recurring audit generation
/// </summary>
public class RecurringAuditOptions
{
    public const string SectionName = "RecurringAudits";

    /// <summary>
    /// Interval between recurring audit generation checks in minutes
    /// </summary>
    public int GenerationIntervalMinutes { get; set; } = 15;

    /// <summary>
    /// Whether to enable recurring audit generation
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Maximum number of audits to generate in a single batch
    /// </summary>
    public int MaxAuditsPerBatch { get; set; } = 100;
}
