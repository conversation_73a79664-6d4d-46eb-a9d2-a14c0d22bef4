using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Exceptions;
using Microsoft.Extensions.Logging;

namespace HWSAuditPlatform.Application.Templates.Commands.UpdateAuditTemplate;

/// <summary>
/// Handler for UpdateAuditTemplateCommand
/// </summary>
public class UpdateAuditTemplateCommandHandler : BaseCommandHandler<UpdateAuditTemplateCommand>
{
    private readonly IAuditTemplateRepository _templateRepository;

    public UpdateAuditTemplateCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        ILogger<UpdateAuditTemplateCommandHandler> logger,
        IAuditTemplateRepository templateRepository)
        : base(context, currentUserService, logger)
    {
        _templateRepository = templateRepository;
    }

    public override async Task Handle(UpdateAuditTemplateCommand request, CancellationToken cancellationToken)
    {
        Logger.LogInformation("Updating audit template with ID: {Id}", request.Id);

        var template = await _templateRepository.GetByIdAsync(request.Id, cancellationToken);
        if (template == null)
        {
            throw new NotFoundException($"Template with ID {request.Id} not found");
        }

        // Check if template is published - published templates cannot be modified
        if (template.IsPublished)
        {
            throw new InvalidOperationException("Cannot modify a published template. Create a new version instead.");
        }

        var currentUserId = CurrentUserService.UserId;
        if (string.IsNullOrEmpty(currentUserId))
        {
            throw new UnauthorizedAccessException("User not authenticated");
        }

        // Update template properties
        template.TemplateName = request.TemplateName;
        template.Description = request.Description;
        template.EnableAreaBasedResponsibility = request.EnableAreaBasedResponsibility;
        template.EnableFindingCategorization = request.EnableFindingCategorization;
        template.IsActive = request.IsActive;
        template.UpdatedAt = DateTime.UtcNow;
        template.UpdatedByUserId = currentUserId;

        // Update record version for optimistic concurrency control
        if (request.RecordVersion?.Length > 0)
        {
            // RecordVersion is handled by the repository/context
        }

        await Context.SaveChangesAsync(cancellationToken);
        await Context.SaveChangesAsync(cancellationToken);

        Logger.LogInformation("Successfully updated audit template with ID: {Id}", request.Id);
    }
}
