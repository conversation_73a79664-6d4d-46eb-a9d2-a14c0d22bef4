using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Templates.Commands.AddQuestion;

/// <summary>
/// Command to add a question to an audit template
/// </summary>
public class AddQuestionCommand : BaseCommand<int>
{
    /// <summary>
    /// ID of the template to add the question to
    /// </summary>
    public int TemplateId { get; set; }

    /// <summary>
    /// Optional question group ID
    /// </summary>
    public int? QuestionGroupId { get; set; }

    /// <summary>
    /// The question text
    /// </summary>
    public string QuestionText { get; set; } = string.Empty;

    /// <summary>
    /// Type of question (YesNo, Numeric, etc.)
    /// </summary>
    public QuestionType QuestionType { get; set; }

    /// <summary>
    /// Display order within the template/group
    /// </summary>
    public int DisplayOrder { get; set; }

    /// <summary>
    /// Whether the question is required
    /// </summary>
    public bool IsRequired { get; set; } = true;

    /// <summary>
    /// Question weight for scoring
    /// </summary>
    public decimal? Weight { get; set; }

    /// <summary>
    /// Help text for the question
    /// </summary>
    public string? HelpText { get; set; }

    /// <summary>
    /// Parent question ID for conditional questions
    /// </summary>
    public int? ParentQuestionId { get; set; }

    /// <summary>
    /// Answer value that triggers this conditional question
    /// </summary>
    public string? TriggerAnswerValue { get; set; }

    /// <summary>
    /// Severity level for findings
    /// </summary>
    public SeverityLevel? SeverityLevel { get; set; }

    /// <summary>
    /// Whether evidence is required for this question
    /// </summary>
    public bool EvidenceRequired { get; set; }

    /// <summary>
    /// List of allowed evidence type IDs
    /// </summary>
    public List<int> AllowedEvidenceTypeIds { get; set; } = new();

    /// <summary>
    /// Question options for single/multi-select questions
    /// </summary>
    public List<QuestionOptionRequest> Options { get; set; } = new();
}

/// <summary>
/// Request model for question options
/// </summary>
public class QuestionOptionRequest
{
    /// <summary>
    /// Display text for the option
    /// </summary>
    public string OptionText { get; set; } = string.Empty;

    /// <summary>
    /// Value stored when this option is selected
    /// </summary>
    public string? OptionValue { get; set; }

    /// <summary>
    /// Display order of the option
    /// </summary>
    public int DisplayOrder { get; set; }
}
