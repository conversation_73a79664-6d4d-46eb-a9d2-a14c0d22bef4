using Microsoft.Extensions.Logging;
using HWSAuditPlatform.Application.Interfaces;

namespace HWSAuditPlatform.Application.Common;

/// <summary>
/// Base class for service requests that don't return a value
/// </summary>
public abstract class BaseRequest
{
}

/// <summary>
/// Base class for service requests that return a value
/// </summary>
/// <typeparam name="TResponse">The type of response returned by the request</typeparam>
public abstract class BaseRequest<TResponse>
{
}

/// <summary>
/// Base class for commands that don't return a value
/// </summary>
public abstract class BaseCommand : BaseRequest
{
}

/// <summary>
/// Base class for commands that return a value
/// </summary>
/// <typeparam name="TResponse">The type of response returned by the command</typeparam>
public abstract class BaseCommand<TResponse> : BaseRequest<TResponse>
{
}

/// <summary>
/// Base class for command handlers that don't return a value
/// </summary>
/// <typeparam name="TCommand">The command type</typeparam>
public abstract class BaseCommandHandler<TCommand> where TCommand : BaseCommand
{
    protected readonly IApplicationDbContext Context;
    protected readonly ICurrentUserService CurrentUserService;
    protected readonly ILogger Logger;

    protected BaseCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        ILogger logger)
    {
        Context = context;
        CurrentUserService = currentUserService;
        Logger = logger;
    }

    public abstract Task Handle(TCommand request, CancellationToken cancellationToken);
}

/// <summary>
/// Base class for command handlers that return a value
/// </summary>
/// <typeparam name="TCommand">The command type</typeparam>
/// <typeparam name="TResponse">The response type</typeparam>
public abstract class BaseCommandHandler<TCommand, TResponse> where TCommand : BaseCommand<TResponse>
{
    protected readonly IApplicationDbContext Context;
    protected readonly ICurrentUserService CurrentUserService;
    protected readonly ILogger Logger;

    protected BaseCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        ILogger logger)
    {
        Context = context;
        CurrentUserService = currentUserService;
        Logger = logger;
    }

    public abstract Task<TResponse> Handle(TCommand request, CancellationToken cancellationToken);
}
